#!/usr/bin/env python3

import sys
import os

try:
    import pandas as pd
    import openpyxl
except ImportError:
    print("需要安装 pandas 和 openpyxl:")
    print("pip3 install pandas openpyxl")
    sys.exit(1)

def analyze_excel_file(file_path):
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    print(f"分析Excel文件: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path) / (1024*1024):.2f} MB")
    
    try:
        # 使用openpyxl直接读取工作表信息
        workbook = openpyxl.load_workbook(file_path, read_only=True)
        sheet_names = workbook.sheetnames
        print(f"工作表数量: {len(sheet_names)}")
        print(f"工作表名称: {sheet_names}")
        
        # 分析第一个工作表
        if sheet_names:
            sheet = workbook[sheet_names[0]]
            max_row = sheet.max_row
            max_col = sheet.max_column
            print(f"\n第一个工作表 '{sheet_names[0]}':")
            print(f"最大行数: {max_row}")
            print(f"最大列数: {max_col}")
            print(f"数据行数（不含表头）: {max_row - 1}")
            
            # 检查表头
            print(f"\n表头信息:")
            for col in range(1, min(max_col + 1, 11)):  # 显示前10列
                cell_value = sheet.cell(row=1, column=col).value
                print(f"  列 {col}: {cell_value}")
            
            # 检查一些数据行
            print(f"\n前5行数据样本:")
            for row in range(2, min(7, max_row + 1)):  # 跳过表头，显示前5行数据
                row_data = []
                for col in range(1, min(6, max_col + 1)):  # 显示前5列
                    cell_value = sheet.cell(row=row, column=col).value
                    row_data.append(str(cell_value) if cell_value is not None else "")
                print(f"  第{row-1}行: {' | '.join(row_data)}")
            
            # 检查最后几行
            print(f"\n最后5行数据样本:")
            start_row = max(max_row - 4, 2)
            for row in range(start_row, max_row + 1):
                row_data = []
                for col in range(1, min(6, max_col + 1)):
                    cell_value = sheet.cell(row=row, column=col).value
                    row_data.append(str(cell_value) if cell_value is not None else "")
                print(f"  第{row-1}行: {' | '.join(row_data)}")
        
        workbook.close()
        
    except Exception as e:
        print(f"使用openpyxl分析失败: {e}")
        
    try:
        # 使用pandas读取
        print(f"\n=== 使用pandas分析 ===")
        df = pd.read_excel(file_path)
        print(f"pandas读取的行数: {len(df)}")
        print(f"pandas读取的列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查空值
        print(f"\n空值统计:")
        for col in df.columns:
            null_count = df[col].isnull().sum()
            if null_count > 0:
                print(f"  {col}: {null_count} 个空值")
        
        # 检查关键字段
        if 'Merchant Txn ID' in df.columns:
            merchant_null = df['Merchant Txn ID'].isnull().sum()
            print(f"\nMerchant Txn ID 为空: {merchant_null} 条")
        
        if 'Trade Order ID' in df.columns:
            trade_null = df['Trade Order ID'].isnull().sum()
            print(f"Trade Order ID 为空: {trade_null} 条")
            
    except Exception as e:
        print(f"使用pandas分析失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python3 simple_excel_check.py <excel_file>")
        print("Example: python3 simple_excel_check.py data/SP20519158_bill_20240701_20250630_28.xlsx")
        sys.exit(1)
    
    file_path = sys.argv[1]
    analyze_excel_file(file_path)
