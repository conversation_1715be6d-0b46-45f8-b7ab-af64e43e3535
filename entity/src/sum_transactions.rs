//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.14

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq)]
#[sea_orm(table_name = "sum_transactions")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub file: String,
    pub request_time: String,
    pub account_subject: String,
    pub account_type: String,
    pub account_currency: String,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub balance_count: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub accounting_amount_in: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub accounting_amount_out: Option<Decimal>,
    #[sea_orm(column_type = "Decimal(Some((20, 2)))", nullable)]
    pub balance_origin: Option<Decimal>,
    pub qty_in: Option<i32>,
    pub qty_out: Option<i32>,
    pub created_at: Option<DateTimeWithTimeZone>,
    pub updated_at: Option<DateTimeWithTimeZone>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {}

impl ActiveModelBehavior for ActiveModel {}
