#!/bin/bash

# 设置脚本在遇到错误时退出
set -e

echo "Setting up PayerMax project with SeaORM workspace structure..."

# 1. 安装 sea-orm-cli
echo "Installing sea-orm-cli..."
cargo install sea-orm-cli

# 2. 复制环境配置文件
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "Please edit .env file with your database credentials before running migrations."
fi

# 3. 运行数据库迁移
echo "Running database migrations..."
echo "Make sure your PostgreSQL database is running and .env file is configured correctly."
read -p "Press Enter to continue with migration or Ctrl+C to cancel..."

# 使用新的migration crate
cargo run -p migration

# 4. 生成 Entity 文件
echo "Generating entity files..."
sea-orm-cli generate entity \
    --database-url "$(grep DATABASE_URL .env | cut -d '=' -f2)" \
    --output-dir entity/src \
    --with-serde both

echo "Setup completed successfully!"
echo ""
echo "SeaORM workspace structure:"
echo "- migration/: Database migration crate"
echo "- entity/: Database entity crate"
echo "- ./: Main application crate"
echo ""
echo "Database tables created:"
echo "- import_records: Tracks file import status and metadata"
echo "- transactions: Stores transaction data with all PayerMax fields"
echo ""
echo "Next steps:"
echo "1. Review the generated entity files in entity/src/"
echo "2. Update entity/src/lib.rs and entity/src/prelude.rs to include the generated entities"
echo "3. Update the import service to use the generated entities"
echo "4. Run 'cargo run' to start the server"
echo ""
echo "Migration commands:"
echo "- Run migrations: cargo run -p migration"
echo "- Generate entities: sea-orm-cli generate entity --database-url \$DATABASE_URL --output-dir entity/src --with-serde both"
echo ""
echo "Test the system with the sample CSV file in test_data/sample.csv"
