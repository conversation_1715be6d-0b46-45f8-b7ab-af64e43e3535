-- 创建 sum_transactions 表的 SQL 脚本
-- 用于手动创建表格，migrate 文件用于后续备用

CREATE TABLE IF NOT EXISTS sum_transactions (
    id BIGSERIAL PRIMARY KEY,
    file VARCHAR NOT NULL,
    request_time VARCHAR NOT NULL,
    account_subject VARCHAR NOT NULL,
    account_type VARCHAR NOT NULL,
    account_currency VARCHAR NOT NULL,
    balance_count DECIMAL(20,2) NOT NULL,
    accounting_amount_in DECIMAL(20,2) NOT NULL DEFAULT 0,
    accounting_amount_out DECIMAL(20,2) NOT NULL DEFAULT 0,
    balance_origin DECIMAL(20,2) NOT NULL,
    qty_in INTEGER NOT NULL DEFAULT 0,
    qty_out INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_sum_transactions_file 
ON sum_transactions (file);

CREATE INDEX IF NOT EXISTS idx_sum_transactions_request_time 
ON sum_transactions (request_time);

CREATE INDEX IF NOT EXISTS idx_sum_transactions_composite 
ON sum_transactions (file, request_time, account_subject, account_currency);

-- 创建更新时间的触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sum_transactions_updated_at 
    BEFORE UPDATE ON sum_transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 验证表创建
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sum_transactions' 
ORDER BY ordinal_position;
