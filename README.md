# PayerMax - 数据录入和统计系统

一个基于 Rust 的数据录入和统计系统，支持 Excel 和 CSV 文件的批量导入到 PostgreSQL 数据库。

## 功能特性

- 支持 Excel (.xlsx, .xls) 和 CSV 文件导入
- 基于 SeaORM 的数据库操作
- RESTful API 接口
- 文件上传和处理状态跟踪
- 错误处理和日志记录

## 技术栈

- **Web 框架**: Salvo.rs
- **数据库 ORM**: SeaORM
- **数据库**: PostgreSQL
- **文件处理**: calamine (Excel), csv (CSV)
- **异步运行时**: Tokio

## 快速开始

### 1. 环境准备

确保您已安装：

- Rust (1.70+)
- PostgreSQL
- sea-orm-cli

```bash
# 安装 sea-orm-cli
cargo install sea-orm-cli
```

### 2. 数据库设置

创建 PostgreSQL 数据库：

```sql
CREATE DATABASE payermax;
```

### 3. 配置环境变量

复制环境配置文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，设置您的数据库连接信息：

```env
DATABASE_URL=postgres://username:password@localhost:5432/payermax
SERVER_HOST=127.0.0.1
SERVER_PORT=8080
UPLOAD_DIR=uploads
RUST_LOG=info
```

### 4. 运行设置脚本

```bash
./setup.sh
```

这个脚本将：

- 安装必要的工具
- 运行数据库迁移
- 生成 Entity 文件

### 5. 手动运行迁移（可选）

```bash
# 运行数据库迁移
cargo run -p migration

# 生成Entity文件
sea-orm-cli generate entity \
  --database-url "postgres://username:password@localhost:5432/payermax" \
  --output-dir entity/src \
  --with-serde both
```

### 6. 启动服务器

```bash
cargo run
```

服务器将在 `http://127.0.0.1:8080` 启动。

## 当前状态

项目已经创建了基本的结构和框架，但需要完成以下步骤才能完全运行：

1. **运行数据库迁移**: `cargo run --bin migrate`
2. **生成 Entity 文件**: 使用 sea-orm-cli 生成实体文件
3. **更新处理器**: 在生成实体后，更新导入和查询功能

### 立即可用的功能

- ✅ 项目结构已创建
- ✅ 依赖项已配置
- ✅ 数据库迁移文件已创建
- ✅ 文件解析器（CSV/Excel）已实现
- ✅ Web API 框架已搭建
- ✅ 基本的文件上传功能

### 需要完成的步骤

- ⏳ 运行数据库迁移
- ⏳ 生成 SeaORM 实体文件
- ⏳ 完善导入服务实现
- ⏳ 添加中间件支持

## API 接口

### 文件上传

```
POST /api/v1/upload
Content-Type: multipart/form-data

参数:
- file: 要上传的文件 (CSV或Excel)
- auto_import: 可选，是否自动开始导入 (true/false)
```

### 开始导入

```
POST /api/v1/import
Content-Type: application/json

{
  "file_id": "上传文件返回的文件ID",
  "file_name": "可选的文件名"
}
```

### 查询导入状态

```
GET /api/v1/import/{import_id}
```

### 列出所有导入

```
GET /api/v1/import/list
```

### 健康检查

```
GET /health
```

## 数据表结构

### import_records 表

- `id`: UUID 主键
- `file_name`: 文件名
- `file_type`: 文件类型 (csv, xlsx, xls)
- `file_size`: 文件大小
- `status`: 导入状态 (pending, processing, completed, failed, completed_with_errors)
- `total_rows`: 总行数
- `processed_rows`: 已处理行数
- `error_rows`: 错误行数
- `error_message`: 错误信息
- `created_at`: 创建时间
- `updated_at`: 更新时间

### transactions 表

- `id`: UUID 主键
- `import_record_id`: 关联的导入记录 ID
- `row_number`: 行号
- `request_time`: 请求时间
- `accounting_amount`: 记账金额 (Decimal 20,2)
- `balance`: 余额 (Decimal 20,2)
- `accounting_currency`: 记账币种 (3 字符)
- `accounting_type`: 记账类型 (32 字符)
- `account_subject`: 账户科目 (64 字符)
- `txn_create_time`: 交易创建时间
- `txn_complete_time`: 交易完成时间
- `merchant_txn_id`: 商户交易 ID (64 字符)
- `trade_order_id`: 贸易订单 ID (64 字符)
- `country`: 国家代码 (2 字符)
- `txn_amount`: 交易金额 (Decimal 20,2)
- `txn_currency`: 交易币种 (3 字符)
- `payee_txn_fee`: 收款方手续费 (Decimal 20,2)
- `payee_txn_fee_currency`: 收款方手续费币种 (3 字符)
- `payer_txn_fee`: 付款方手续费 (Decimal 20,2)
- `payer_txn_fee_currency`: 付款方手续费币种 (3 字符)
- `payee_tax`: 收款方税费 (Decimal 20,2)
- `payee_tax_currency`: 收款方税费币种 (3 字符)
- `payer_tax`: 付款方税费 (Decimal 20,2)
- `payer_tax_currency`: 付款方税费币种 (3 字符)
- `remark`: 备注 (Text)
- `created_at`: 创建时间

## Excel/CSV 文件格式

系统已配置为处理 PayerMax 交易数据格式，包含以下字段：

- Request Time - 请求时间
- Accounting Amount - 记账金额
- Balance - 余额
- Accounting Currency - 记账币种
- Accounting Type - 记账类型
- Account Subject - 账户科目
- Txn Create Time - 交易创建时间
- Txn complete Time - 交易完成时间
- Merchant Txn ID - 商户交易 ID
- Trade Order ID - 贸易订单 ID
- Country - 国家
- Txn Amount - 交易金额
- Txn Currency - 交易币种
- Payee Txn Fee - 收款方手续费
- Payee Txn Fee Currency - 收款方手续费币种
- Payer Txn Fee - 付款方手续费
- Payer Txn Fee Currency - 付款方手续费币种
- Payee Tax - 收款方税费
- Payee Tax Currency - 收款方税费币种
- Payer Tax - 付款方税费
- Payer Tax Currency - 付款方税费币种
- Remark - 备注

参考 `test_data/sample.csv` 文件查看具体格式示例。

## 开发

### 运行测试

```bash
cargo test
```

### 查看日志

```bash
RUST_LOG=debug cargo run
```

### 重新生成 Entity

```bash
sea-orm-cli generate entity \
    --database-url "postgres://username:password@localhost:5432/payermax" \
    --output-dir entity/src \
    --with-serde both
```

## 项目结构

项目采用 SeaORM 推荐的 workspace 结构：

```
payermax/
├── Cargo.toml              # Workspace配置
├── README.md               # 项目文档
├── setup.sh                # 自动化设置脚本
├── .env.example            # 环境变量模板
├── src/                    # 主应用代码
│   ├── main.rs             # 应用入口
│   ├── lib.rs              # 库入口
│   ├── config/mod.rs       # 配置管理
│   ├── error/mod.rs        # 错误处理
│   ├── service/            # 业务逻辑服务
│   └── handler/            # Web API处理器
├── entity/                 # 数据库实体crate
│   ├── Cargo.toml          # Entity crate配置
│   └── src/
│       ├── lib.rs          # Entity库入口
│       └── prelude.rs      # Entity预导入
├── migration/              # 数据库迁移crate
│   ├── Cargo.toml          # Migration crate配置
│   └── src/
│       ├── lib.rs          # Migration库入口
│       ├── main.rs         # Migration CLI入口
│       ├── m20240101_000001_create_import_records.rs
│       └── m20240101_000002_create_transactions.rs
├── test_data/sample.csv    # 测试数据
└── uploads/                # 文件上传目录
```

### Workspace 命令

- 编译整个 workspace: `cargo build`
- 运行主应用: `cargo run`
- 运行迁移: `cargo run -p migration`
- 检查特定 crate: `cargo check -p entity`

## 许可证

MIT License
