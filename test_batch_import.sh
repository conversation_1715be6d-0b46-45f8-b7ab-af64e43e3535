#!/bin/bash

# 测试批量导入功能的脚本

echo "=== PayerMax 批量导入测试 ==="

# 检查 data 目录是否存在
if [ ! -d "data" ]; then
    echo "❌ data 目录不存在，请确保有数据文件可供导入"
    exit 1
fi

# 检查 data 目录中的文件
echo "📁 检查 data 目录中的文件："
ls -la data/

echo ""
echo "🚀 启动批量导入..."

# 使用命令行工具进行批量导入
cargo run --bin batch_import data 8 1000

echo ""
echo "✅ 批量导入测试完成"

# 可选：检查数据库中的数据
echo ""
echo "📊 检查导入结果（需要 psql 客户端）："
echo "可以运行以下命令检查数据："
echo "psql -h 192.168.10.4 -p 54321 -U ferycmsdb -d payermax -c \"SELECT filename, COUNT(*) FROM transactions GROUP BY filename;\""
