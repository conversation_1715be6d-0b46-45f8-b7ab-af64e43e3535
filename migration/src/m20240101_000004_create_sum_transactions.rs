use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(SumTransactions::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(SumTransactions::Id)
                            .big_integer()
                            .not_null()
                            .auto_increment()
                            .primary_key(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::File)
                            .string()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::RequestTime)
                            .string()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::AccountSubject)
                            .string()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::AccountType)
                            .string()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::AccountCurrency)
                            .string()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::BalanceCount)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::AccountingAmountIn)
                            .decimal_len(20, 2)
                            .not_null()
                            .default(0),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::AccountingAmountOut)
                            .decimal_len(20, 2)
                            .not_null()
                            .default(0),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::BalanceOrigin)
                            .decimal_len(20, 2)
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::QtyIn)
                            .integer()
                            .not_null()
                            .default(0),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::QtyOut)
                            .integer()
                            .not_null()
                            .default(0),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::CreatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .col(
                        ColumnDef::new(SumTransactions::UpdatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .to_owned(),
            )
            .await?;

        // 创建索引以提高查询性能
        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_sum_transactions_file")
                    .table(SumTransactions::Table)
                    .col(SumTransactions::File)
                    .to_owned(),
            )
            .await?;

        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_sum_transactions_request_time")
                    .table(SumTransactions::Table)
                    .col(SumTransactions::RequestTime)
                    .to_owned(),
            )
            .await?;

        manager
            .create_index(
                Index::create()
                    .if_not_exists()
                    .name("idx_sum_transactions_composite")
                    .table(SumTransactions::Table)
                    .col(SumTransactions::File)
                    .col(SumTransactions::RequestTime)
                    .col(SumTransactions::AccountSubject)
                    .col(SumTransactions::AccountCurrency)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(SumTransactions::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
enum SumTransactions {
    Table,
    Id,
    File,
    RequestTime,
    AccountSubject,
    AccountType,
    AccountCurrency,
    BalanceCount,
    AccountingAmountIn,
    AccountingAmountOut,
    BalanceOrigin,
    QtyIn,
    QtyOut,
    CreatedAt,
    UpdatedAt,
}
