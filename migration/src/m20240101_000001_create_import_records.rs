use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .create_table(
                Table::create()
                    .table(ImportRecord::Table)
                    .if_not_exists()
                    .col(
                        ColumnDef::new(ImportRecord::Id)
                            .uuid()
                            .not_null()
                            .primary_key(),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::FileName)
                            .string()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::FileType)
                            .string()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::FileSize)
                            .big_integer()
                            .not_null(),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::Status)
                            .string()
                            .not_null()
                            .default("pending"),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::TotalRows)
                            .integer()
                            .null(),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::ProcessedRows)
                            .integer()
                            .default(0),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::ErrorRows)
                            .integer()
                            .default(0),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::ErrorMessage)
                            .text()
                            .null(),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::CreatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .col(
                        ColumnDef::new(ImportRecord::UpdatedAt)
                            .timestamp_with_time_zone()
                            .not_null()
                            .default(Expr::current_timestamp()),
                    )
                    .to_owned(),
            )
            .await
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        manager
            .drop_table(Table::drop().table(ImportRecord::Table).to_owned())
            .await
    }
}

#[derive(DeriveIden)]
pub enum ImportRecord {
    Table,
    Id,
    FileName,
    FileType,
    FileSize,
    Status,
    TotalRows,
    ProcessedRows,
    ErrorRows,
    ErrorMessage,
    CreatedAt,
    UpdatedAt,
}
