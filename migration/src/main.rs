use sea_orm_migration::prelude::*;
use std::env;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct Config {
    database_url: String,
}

impl Config {
    fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        // 首先尝试使用完整的DATABASE_URL
        if let Ok(database_url) = env::var("DATABASE_URL") {
            return Ok(Config { database_url });
        }

        // 如果没有DATABASE_URL，尝试从分离的环境变量构建
        let host = env::var("DB_HOST").unwrap_or_else(|_| "localhost".to_string());
        let port = env::var("DB_PORT").unwrap_or_else(|_| "5432".to_string());
        let username = env::var("DB_USERNAME").or_else(|_| env::var("DB_USER"))?;
        let password = env::var("DB_PASSWORD").or_else(|_| env::var("DB_PASS"))?;
        let database = env::var("DB_DATABASE").or_else(|_| env::var("DB_NAME"))?;

        // URL编码密码中的特殊字符
        let encoded_password = urlencoding::encode(&password);

        let database_url = format!(
            "postgres://{}:{}@{}:{}/{}",
            username, encoded_password, host, port, database
        );

        Ok(Config { database_url })
    }
}

#[async_std::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenvy::dotenv().ok();

    let config = Config::from_env()?;

    println!("Connecting to database: {}", config.database_url);

    // 设置连接选项，增加超时时间和重试
    let mut opt = sea_orm::ConnectOptions::new(&config.database_url);
    opt.max_connections(1)
        .min_connections(1)
        .connect_timeout(std::time::Duration::from_secs(10))
        .acquire_timeout(std::time::Duration::from_secs(10))
        .idle_timeout(std::time::Duration::from_secs(10))
        .max_lifetime(std::time::Duration::from_secs(10));

    let db = match sea_orm::Database::connect(opt).await {
        Ok(db) => {
            println!("Database connection established successfully!");

            // 测试数据库连接
            match db.ping().await {
                Ok(_) => println!("Database ping successful!"),
                Err(e) => {
                    eprintln!("Database ping failed: {}", e);
                    return Err(e.into());
                }
            }

            db
        }
        Err(e) => {
            eprintln!("Failed to connect to database: {}", e);
            eprintln!("Please check:");
            eprintln!("1. PostgreSQL is running");
            eprintln!("2. Database 'payermax' exists");
            eprintln!("3. User 'ferycmsdb' has access to the database");
            eprintln!("4. Password is correct");
            eprintln!("5. Host and port are correct");
            return Err(e.into());
        }
    };

    println!("Running migrations...");
    migration::Migrator::up(&db, None).await?;

    println!("Migration completed successfully!");

    Ok(())
}
