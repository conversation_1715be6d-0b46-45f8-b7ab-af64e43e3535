[package]
name = "migration"
version = "0.1.0"
edition = "2021"
publish = false

[lib]
name = "migration"
path = "src/lib.rs"

[dependencies]
async-std = { version = "1", features = ["attributes", "tokio1"] }
dotenvy = "0.15"
urlencoding = "2.1"
sea-orm = { version = "1.1.14", features = [
  "sqlx-postgres",
  "runtime-tokio-rustls",
] }

[dependencies.sea-orm-migration]
version = "1.1.14"
features = [
  "runtime-tokio-rustls", # `ASYNC_RUNTIME` feature
  "sqlx-postgres",        # `DATABASE_DRIVER` feature
]
