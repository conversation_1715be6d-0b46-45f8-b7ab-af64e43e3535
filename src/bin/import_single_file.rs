use payermax::config::Config;
use payermax::service::batch_import_service::BatchImportService;
use payermax::service::file_parser::FileParser;
use sea_orm::{Database, EntityTrait, ColumnTrait, QueryFilter, PaginatorTrait};
use entity::transactions;
use std::env;
use std::path::Path;
use tracing::{error, info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 获取命令行参数
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        eprintln!("Usage: {} <file_path> [batch_size]", args[0]);
        eprintln!("Example: {} data/SP20519158_bill_20240701_20250630_28.xlsx 1000", args[0]);
        std::process::exit(1);
    }

    let file_path = Path::new(&args[1]);
    let batch_size = args.get(2)
        .and_then(|s| s.parse::<usize>().ok())
        .unwrap_or(1000);
    
    if !file_path.exists() {
        error!("File does not exist: {}", file_path.display());
        std::process::exit(1);
    }

    if !file_path.is_file() {
        error!("Path is not a file: {}", file_path.display());
        std::process::exit(1);
    }

    let filename = file_path.file_name()
        .and_then(|name| name.to_str())
        .ok_or("Invalid filename")?;

    info!("Starting smart single file import:");
    info!("  File: {}", file_path.display());
    info!("  Filename: {}", filename);
    info!("  Batch size: {}", batch_size);

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    info!("Connected to database");

    // 解析Excel文件
    info!("Parsing Excel file...");
    let parse_result = FileParser::parse_file(file_path)?;
    let total_excel_rows = parse_result.rows.len();
    info!("Excel file contains {} data rows", total_excel_rows);

    // 检查数据库中已存在的记录数
    let existing_count = transactions::Entity::find()
        .filter(transactions::Column::Filename.eq(filename))
        .count(&db)
        .await?;
    
    info!("Database contains {} existing records for this file", existing_count);

    if existing_count as usize >= total_excel_rows {
        info!("✅ All records already imported. No action needed.");
        return Ok(());
    }

    let missing_count = total_excel_rows - existing_count as usize;
    info!("📊 Missing {} records, will use BatchImportService to import", missing_count);

    // 使用BatchImportService的单文件导入功能
    let batch_service = BatchImportService::new(db.clone()).with_batch_size(batch_size);

    info!("Starting import using BatchImportService...");
    match batch_service.import_single_file(file_path).await {
        Ok(progress) => {
            info!("✅ Import completed successfully!");
            info!("📊 Import Statistics:");
            info!("  Total files: {}", progress.total_files);
            info!("  Processed files: {}", progress.processed_files);
            info!("  Total records: {}", progress.total_records);
            info!("  Processed records: {}", progress.processed_records);
            
            if !progress.failed_files.is_empty() {
                warn!("⚠️  Failed files: {:?}", progress.failed_files);
            }
            
            // 验证最终结果
            let final_count = transactions::Entity::find()
                .filter(transactions::Column::Filename.eq(filename))
                .count(&db)
                .await?;
            
            info!("📈 Final database count: {}", final_count);
            
            if final_count as usize == total_excel_rows {
                info!("🎉 SUCCESS: All Excel records are now in the database!");
            } else {
                warn!("⚠️  WARNING: {} records are still missing", total_excel_rows - final_count as usize);
            }
        }
        Err(e) => {
            error!("❌ Import failed: {}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}
