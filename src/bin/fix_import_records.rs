use chrono::Utc;
use entity::import_record::{ActiveModel as ImportRecordActiveModel, Entity as ImportRecord};
use entity::transactions::Entity as Transactions;
use payermax::config::Config;
use payermax::service::file_parser::FileParser;
use sea_orm::{ActiveModelTrait, ColumnTrait, Database, EntityTrait, QuerySelect, Set};
use std::path::Path;
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    info!("Connected to database");

    println!("🔧 修复 Import Records 工具");
    println!("============================");

    // 1. 获取所有已导入的文件和其记录数
    let transactions_stats = Transactions::find()
        .select_only()
        .column_as(entity::transactions::Column::Filename, "filename")
        .column_as(entity::transactions::Column::Id.count(), "count")
        .group_by(entity::transactions::Column::Filename)
        .into_tuple::<(String, i64)>()
        .all(&db)
        .await?;

    // 2. 获取所有现有的 import_record
    let import_records = ImportRecord::find().all(&db).await?;
    let mut import_record_map = std::collections::HashMap::new();
    for record in import_records {
        import_record_map.insert(record.file_name.clone(), record);
    }

    println!(
        "📊 检查 {} 个文件的 import_record 状态",
        transactions_stats.len()
    );

    let mut fixed_records = 0;
    let mut correct_records = 0;
    let mut problematic_files = Vec::new();

    for (filename, db_count) in &transactions_stats {
        let file_path = Path::new("data").join(filename);

        if !file_path.exists() {
            warn!("文件不存在: {}", filename);
            continue;
        }

        // 解析 Excel 文件获取实际行数
        let excel_rows = match FileParser::parse_file(&file_path) {
            Ok(parse_result) => parse_result.rows.len() as i64,
            Err(e) => {
                warn!("无法解析文件 {}: {}", filename, e);
                continue;
            }
        };

        // 检查是否存在 import_record
        if let Some(import_record) = import_record_map.get(filename.as_str()) {
            let recorded_total = import_record.total_rows.unwrap_or(0) as i64;

            if recorded_total != excel_rows {
                println!("\n⚠️  发现问题文件: {}", filename);
                println!("   Excel 实际行数: {}", excel_rows);
                println!("   数据库记录数: {}", db_count);
                println!("   Import Record 记录的 total_rows: {}", recorded_total);

                // 判断是否需要修复
                if recorded_total == *db_count && excel_rows != *db_count {
                    // 这种情况说明 import_record 记录的是数据库记录数，而不是 Excel 实际行数
                    println!("   🔧 需要修复: 更新 total_rows 为 Excel 实际行数");

                    // 更新 import_record
                    let mut active_model: ImportRecordActiveModel = import_record.clone().into();
                    active_model.total_rows = Set(Some(excel_rows as i32));
                    active_model.updated_at = Set(Utc::now().into());

                    // 如果数据库记录数少于 Excel 行数，状态应该是 "partial" 而不是 "completed"
                    if *db_count < excel_rows {
                        active_model.status = Set("partial".to_string());
                        active_model.processed_rows = Set(Some(*db_count as i32));
                        active_model.error_rows = Set(Some((excel_rows - db_count) as i32));
                        active_model.error_message = Set(Some(format!(
                            "Partial import: {} out of {} rows imported",
                            db_count, excel_rows
                        )));
                    }

                    match active_model.update(&db).await {
                        Ok(_) => {
                            println!("   ✅ 已修复 import_record");
                            fixed_records += 1;
                        }
                        Err(e) => {
                            println!("   ❌ 修复失败: {}", e);
                        }
                    }

                    problematic_files.push((
                        filename.clone(),
                        excel_rows,
                        *db_count,
                        recorded_total,
                    ));
                } else {
                    println!("   ℹ️  情况复杂，需要手动检查");
                }
            } else {
                // 记录正确
                correct_records += 1;
                if excel_rows == *db_count {
                    info!("✅ 文件 {} 完整且记录正确", filename);
                } else {
                    println!(
                        "⚠️  文件 {} 记录正确但数据不完整 (Excel: {}, DB: {})",
                        filename, excel_rows, db_count
                    );
                }
            }
        } else {
            // 没有 import_record，需要创建
            println!("\n📝 创建缺失的 import_record: {}", filename);

            let file_size = std::fs::metadata(&file_path)?.len() as i64;
            let file_extension = file_path
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("unknown");

            let status = if excel_rows == *db_count {
                "completed"
            } else {
                "partial"
            };

            let error_rows = if excel_rows > *db_count {
                Some((excel_rows - db_count) as i32)
            } else {
                Some(0)
            };

            let error_message = if excel_rows > *db_count {
                Some(format!(
                    "Partial import: {} out of {} rows imported",
                    db_count, excel_rows
                ))
            } else {
                None
            };

            let import_record = ImportRecordActiveModel {
                id: Set(uuid::Uuid::new_v4()),
                file_name: Set(filename.clone()),
                file_type: Set(file_extension.to_string()),
                file_size: Set(file_size),
                status: Set(status.to_string()),
                total_rows: Set(Some(excel_rows as i32)),
                processed_rows: Set(Some(*db_count as i32)),
                error_rows: Set(error_rows),
                error_message: Set(error_message),
                created_at: Set(Utc::now().into()),
                updated_at: Set(Utc::now().into()),
            };

            match import_record.insert(&db).await {
                Ok(_) => {
                    println!("   ✅ 已创建 import_record");
                    fixed_records += 1;
                }
                Err(e) => {
                    println!("   ❌ 创建失败: {}", e);
                }
            }
        }
    }

    println!("\n📊 修复结果统计:");
    println!("总文件数: {}", transactions_stats.len());
    println!("正确记录数: {}", correct_records);
    println!("修复/创建记录数: {}", fixed_records);

    if !problematic_files.is_empty() {
        println!("\n🔍 修复的问题文件详情:");
        for (filename, excel_rows, db_count, old_total) in problematic_files {
            println!("  📁 {}", filename);
            println!(
                "     Excel 行数: {} → 数据库记录: {} (缺失: {})",
                excel_rows,
                db_count,
                excel_rows - db_count
            );
            println!(
                "     修复前 total_rows: {} → 修复后: {}",
                old_total, excel_rows
            );
        }
    }

    println!("\n💡 建议:");
    println!("1. 重新运行 generate_sum_transactions 工具");
    println!("2. 现在只有完整导入的文件会被处理");
    println!("3. 对于不完整的文件，考虑重新导入");

    Ok(())
}
