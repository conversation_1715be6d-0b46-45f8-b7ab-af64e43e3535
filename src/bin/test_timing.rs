use entity::transactions::{Column as TransactionsColumn, Entity as Transactions};
use payermax::config::Config;
use payermax::service::file_parser::FileParser;
use sea_orm::{
    ColumnTrait, Database, EntityTrait, PaginatorTrait,
    QueryFilter, ConnectionTrait,
};
use std::path::Path;
use std::time::Instant;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    let filename = "SP20519158_bill_20240701_20250630_28.xlsx";
    println!("🎯 测试文件: {}", filename);
    
    // 测试分区查询
    println!("\n1. 测试分区查询:");
    let partition_name = sanitize_partition_name(filename);
    println!("🔍 分区名: transactions_{}", partition_name);
    
    let db_start = Instant::now();
    let count_sql = format!(
        "SELECT COUNT(*) as count FROM transactions_{} WHERE filename = '{}'",
        partition_name,
        filename.replace("'", "''")
    );
    
    let result = db
        .query_one(sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            count_sql,
        ))
        .await?;
    
    if let Some(row) = result {
        let count: i64 = row.try_get("", "count")?;
        let db_duration = db_start.elapsed();
        println!("✅ 分区查询结果: {} 条记录", count);
        println!("⏱️  分区查询耗时: {:?}", db_duration);
    }
    
    // 测试普通查询
    println!("\n2. 测试普通查询:");
    let db_start = Instant::now();
    let count = Transactions::find()
        .filter(TransactionsColumn::Filename.eq(filename))
        .count(&db)
        .await?;
    let db_duration = db_start.elapsed();
    println!("✅ 普通查询结果: {} 条记录", count);
    println!("⏱️  普通查询耗时: {:?}", db_duration);
    
    // 测试Excel解析
    println!("\n3. 测试Excel解析:");
    let file_path = Path::new("data").join(filename);
    let excel_start = Instant::now();
    match FileParser::parse_file(&file_path) {
        Ok(parse_result) => {
            let excel_duration = excel_start.elapsed();
            println!("✅ Excel解析结果: {} 行数据", parse_result.rows.len());
            println!("⏱️  Excel解析耗时: {:?}", excel_duration);
        }
        Err(e) => {
            let excel_duration = excel_start.elapsed();
            println!("❌ Excel解析失败: {}", e);
            println!("⏱️  Excel解析耗时: {:?}", excel_duration);
        }
    }

    Ok(())
}

// 生成安全的分区名称
fn sanitize_partition_name(filename: &str) -> String {
    filename
        .to_lowercase()
        .replace(".", "_")
        .replace("-", "_")
        .replace(" ", "_")
        .chars()
        .filter(|c| c.is_alphanumeric() || *c == '_')
        .collect()
}
