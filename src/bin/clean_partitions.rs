use payermax::config::Config;
use sea_orm::{Database, ConnectionTrait};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    // 查询所有分区表
    let query = r#"
        SELECT tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'transactions_%' 
        ORDER BY tablename;
    "#;
    
    let partitions = db.query_all(sea_orm::Statement::from_string(
        sea_orm::DatabaseBackend::Postgres,
        query.to_string(),
    )).await?;

    println!("Found {} partition tables:", partitions.len());
    
    for partition in &partitions {
        let table_name: String = partition.try_get("", "tablename")?;
        println!("  - {}", table_name);
    }

    if !partitions.is_empty() {
        println!("\nDropping all partition tables...");
        
        for partition in partitions {
            let table_name: String = partition.try_get("", "tablename")?;
            let drop_sql = format!("DROP TABLE IF EXISTS {} CASCADE", table_name);
            
            db.execute(sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                drop_sql,
            )).await?;
            
            println!("Dropped: {}", table_name);
        }
        
        println!("All partition tables dropped successfully!");
    } else {
        println!("No partition tables found.");
    }

    Ok(())
}
