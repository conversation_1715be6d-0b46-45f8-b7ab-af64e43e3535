use entity::sum_transactions::Entity as SumTransactions;
use payermax::config::Config;
use sea_orm::{
    ColumnTrait, Database, EntityTrait, PaginatorTrait, QueryFilter, QueryOrder, QuerySelect,
};
use std::collections::HashMap;
use tracing::info;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 解析命令行参数
    let args: Vec<String> = std::env::args().collect();
    let command = args.get(1).map(|s| s.as_str()).unwrap_or("summary");

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    info!("Connected to database");

    match command {
        "summary" => show_summary(&db).await?,
        "by-file" => {
            let filename = args.get(2);
            show_by_file(&db, filename).await?;
        }
        "by-time" => {
            let time_period = args.get(2);
            show_by_time(&db, time_period).await?;
        }
        "by-currency" => {
            let currency = args.get(2);
            show_by_currency(&db, currency).await?;
        }
        "validate" => validate_data(&db).await?,
        _ => {
            println!("📊 PayerMax 汇总数据查询工具");
            println!("==============================");
            println!("用法:");
            println!("  {} summary              - 显示总体汇总", args[0]);
            println!("  {} by-file [filename]   - 按文件查询", args[0]);
            println!("  {} by-time [yyyyMM]     - 按时间查询", args[0]);
            println!("  {} by-currency [currency] - 按货币查询", args[0]);
            println!("  {} validate             - 验证数据完整性", args[0]);
        }
    }

    Ok(())
}

async fn show_summary(db: &sea_orm::DatabaseConnection) -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 汇总数据总览");
    println!("================");

    // 基础统计
    let total_records = SumTransactions::find().count(db).await?;
    println!("总汇总记录数: {}", total_records);

    // 按文件统计
    let file_stats = SumTransactions::find()
        .select_only()
        .column_as(entity::sum_transactions::Column::File, "file")
        .column_as(entity::sum_transactions::Column::Id.count(), "count")
        .group_by(entity::sum_transactions::Column::File)
        .into_tuple::<(String, i64)>()
        .all(db)
        .await?;

    println!("\n📁 按文件统计:");
    for (filename, count) in &file_stats {
        println!("  {}: {} 条汇总", filename, count);
    }

    // 按时间统计
    let time_stats = SumTransactions::find()
        .select_only()
        .column_as(
            entity::sum_transactions::Column::RequestTime,
            "request_time",
        )
        .column_as(entity::sum_transactions::Column::Id.count(), "count")
        .group_by(entity::sum_transactions::Column::RequestTime)
        .order_by_asc(entity::sum_transactions::Column::RequestTime)
        .into_tuple::<(String, i64)>()
        .all(db)
        .await?;

    println!("\n📅 按时间统计:");
    for (time_period, count) in &time_stats {
        println!("  {}: {} 条汇总", time_period, count);
    }

    // 按货币统计
    let currency_stats = SumTransactions::find()
        .select_only()
        .column_as(
            entity::sum_transactions::Column::AccountCurrency,
            "currency",
        )
        .column_as(entity::sum_transactions::Column::Id.count(), "count")
        .group_by(entity::sum_transactions::Column::AccountCurrency)
        .order_by_desc(entity::sum_transactions::Column::Id.count())
        .into_tuple::<(String, i64)>()
        .all(db)
        .await?;

    println!("\n💱 按货币统计:");
    for (currency, count) in &currency_stats {
        println!("  {}: {} 条汇总", currency, count);
    }

    Ok(())
}

async fn show_by_file(
    db: &sea_orm::DatabaseConnection,
    filename: Option<&String>,
) -> Result<(), Box<dyn std::error::Error>> {
    match filename {
        Some(file) => {
            println!("📁 文件汇总: {}", file);
            println!("========================");

            let records = SumTransactions::find()
                .filter(entity::sum_transactions::Column::File.eq(file))
                .order_by_asc(entity::sum_transactions::Column::RequestTime)
                .order_by_asc(entity::sum_transactions::Column::AccountSubject)
                .order_by_asc(entity::sum_transactions::Column::AccountCurrency)
                .all(db)
                .await?;

            if records.is_empty() {
                println!("未找到该文件的汇总数据");
                return Ok(());
            }

            println!("时间    | 账户主体                    | 类型        | 货币 | 进账金额    | 出账金额    | 余额        | 进账笔数 | 出账笔数");
            println!("--------|----------------------------|-------------|------|-------------|-------------|-------------|----------|----------");

            for record in records {
                println!(
                    "{} | {:26} | {:11} | {:4} | {:11.2} | {:11.2} | {:11.2} | {:8} | {:8}",
                    record.request_time,
                    record.account_subject,
                    record.account_type,
                    record.account_currency,
                    record.accounting_amount_in.unwrap_or_default(),
                    record.accounting_amount_out.unwrap_or_default(),
                    record.balance_count.unwrap_or_default(),
                    record.qty_in.unwrap_or_default(),
                    record.qty_out.unwrap_or_default()
                );
            }
        }
        None => {
            println!("📁 所有文件列表:");
            println!("================");

            let files = SumTransactions::find()
                .select_only()
                .column(entity::sum_transactions::Column::File)
                .distinct()
                .into_tuple::<String>()
                .all(db)
                .await?;

            for (index, file) in files.iter().enumerate() {
                println!("{}. {}", index + 1, file);
            }
        }
    }

    Ok(())
}

async fn show_by_time(
    db: &sea_orm::DatabaseConnection,
    time_period: Option<&String>,
) -> Result<(), Box<dyn std::error::Error>> {
    match time_period {
        Some(period) => {
            println!("📅 时间段汇总: {}", period);
            println!("====================");

            let records = SumTransactions::find()
                .filter(entity::sum_transactions::Column::RequestTime.eq(period))
                .order_by_asc(entity::sum_transactions::Column::AccountSubject)
                .order_by_asc(entity::sum_transactions::Column::AccountCurrency)
                .all(db)
                .await?;

            if records.is_empty() {
                println!("未找到该时间段的汇总数据");
                return Ok(());
            }

            println!("文件                                    | 账户主体                    | 类型        | 货币 | 进账金额    | 出账金额    | 余额");
            println!("----------------------------------------|----------------------------|-------------|------|-------------|-------------|-------------");

            for record in records {
                println!(
                    "{:39} | {:26} | {:11} | {:4} | {:11.2} | {:11.2} | {:11.2}",
                    record.file,
                    record.account_subject,
                    record.account_type,
                    record.account_currency,
                    record.accounting_amount_in.unwrap_or_default(),
                    record.accounting_amount_out.unwrap_or_default(),
                    record.balance_count.unwrap_or_default()
                );
            }
        }
        None => {
            println!("📅 所有时间段列表:");
            println!("==================");

            let periods = SumTransactions::find()
                .select_only()
                .column(entity::sum_transactions::Column::RequestTime)
                .distinct()
                .order_by_asc(entity::sum_transactions::Column::RequestTime)
                .into_tuple::<String>()
                .all(db)
                .await?;

            for period in periods {
                println!("  {}", period);
            }
        }
    }

    Ok(())
}

async fn show_by_currency(
    db: &sea_orm::DatabaseConnection,
    currency: Option<&String>,
) -> Result<(), Box<dyn std::error::Error>> {
    match currency {
        Some(curr) => {
            println!("💱 货币汇总: {}", curr);
            println!("================");

            let records = SumTransactions::find()
                .filter(entity::sum_transactions::Column::AccountCurrency.eq(curr))
                .order_by_asc(entity::sum_transactions::Column::RequestTime)
                .order_by_asc(entity::sum_transactions::Column::AccountSubject)
                .all(db)
                .await?;

            if records.is_empty() {
                println!("未找到该货币的汇总数据");
                return Ok(());
            }

            // 按时间段汇总
            let mut time_summary: HashMap<
                String,
                (
                    rust_decimal::Decimal,
                    rust_decimal::Decimal,
                    rust_decimal::Decimal,
                ),
            > = HashMap::new();

            for record in &records {
                let entry = time_summary.entry(record.request_time.clone()).or_insert((
                    rust_decimal::Decimal::new(0, 0),
                    rust_decimal::Decimal::new(0, 0),
                    rust_decimal::Decimal::new(0, 0),
                ));
                entry.0 += record.accounting_amount_in.unwrap_or_default();
                entry.1 += record.accounting_amount_out.unwrap_or_default();
                entry.2 += record.balance_count.unwrap_or_default();
            }

            println!("时间段 | 总进账金额  | 总出账金额  | 总余额");
            println!("-------|-------------|-------------|-------------");

            let mut sorted_times: Vec<_> = time_summary.iter().collect();
            sorted_times.sort_by_key(|(time, _)| *time);

            for (time, (amount_in, amount_out, balance)) in sorted_times {
                println!(
                    "{} | {:11.2} | {:11.2} | {:11.2}",
                    time, amount_in, amount_out, balance
                );
            }
        }
        None => {
            println!("💱 所有货币列表:");
            println!("================");

            let currencies = SumTransactions::find()
                .select_only()
                .column(entity::sum_transactions::Column::AccountCurrency)
                .distinct()
                .order_by_asc(entity::sum_transactions::Column::AccountCurrency)
                .into_tuple::<String>()
                .all(db)
                .await?;

            for currency in currencies {
                println!("  {}", currency);
            }
        }
    }

    Ok(())
}

async fn validate_data(db: &sea_orm::DatabaseConnection) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 验证汇总数据完整性");
    println!("======================");

    let records = SumTransactions::find().all(db).await?;

    let mut validation_issues = Vec::new();

    for record in &records {
        // 验证 balance_count 计算是否正确
        let expected_balance = record.balance_origin.unwrap_or_default()
            - record.accounting_amount_in.unwrap_or_default()
            - record.accounting_amount_out.unwrap_or_default();

        let actual_balance = record.balance_count.unwrap_or_default();

        if (expected_balance - actual_balance).abs() > rust_decimal::Decimal::new(1, 2) {
            // 允许0.01的误差
            validation_issues.push(format!(
                "ID {}: balance_count 计算错误 (期望: {}, 实际: {})",
                record.id, expected_balance, actual_balance
            ));
        }

        // 验证数量是否合理
        if record.qty_in.unwrap_or_default() < 0 || record.qty_out.unwrap_or_default() < 0 {
            validation_issues.push(format!(
                "ID {}: 数量不能为负数 (qty_in: {}, qty_out: {})",
                record.id,
                record.qty_in.unwrap_or_default(),
                record.qty_out.unwrap_or_default()
            ));
        }
    }

    if validation_issues.is_empty() {
        println!("✅ 所有汇总数据验证通过！");
    } else {
        println!("❌ 发现 {} 个验证问题:", validation_issues.len());
        for issue in validation_issues {
            println!("  - {}", issue);
        }
    }

    println!("\n📊 验证统计:");
    println!("总记录数: {}", records.len());

    Ok(())
}
