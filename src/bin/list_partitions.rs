use payermax::config::Config;
use sea_orm::{Database, ConnectionTrait};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    // 查询所有分区表
    let partition_sql = "
        SELECT tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'transactions_%' 
        AND schemaname = 'public'
        ORDER BY tablename
    ";
    
    let results = db
        .query_all(sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            partition_sql.to_string(),
        ))
        .await?;
    
    println!("🔍 找到的分区表:");
    if results.is_empty() {
        println!("  ❌ 没有找到任何分区表");
    } else {
        for (i, row) in results.iter().enumerate() {
            let table_name: String = row.try_get("", "tablename")?;
            println!("  {}. {}", i + 1, table_name);
        }
    }

    // 检查主表结构
    let main_table_sql = "
        SELECT tablename 
        FROM pg_tables 
        WHERE tablename = 'transactions' 
        AND schemaname = 'public'
    ";
    
    let main_result = db
        .query_all(sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            main_table_sql.to_string(),
        ))
        .await?;
    
    if main_result.is_empty() {
        println!("❌ 主表 'transactions' 不存在");
    } else {
        println!("✅ 主表 'transactions' 存在");
        
        // 检查是否是分区表
        let partition_info_sql = "
            SELECT 
                schemaname,
                tablename,
                partitiontype
            FROM pg_partitioned_tables 
            WHERE schemaname = 'public' AND tablename = 'transactions'
        ";
        
        let partition_info = db
            .query_all(sea_orm::Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                partition_info_sql.to_string(),
            ))
            .await?;
        
        if partition_info.is_empty() {
            println!("📊 'transactions' 是普通表（非分区表）");
        } else {
            println!("📊 'transactions' 是分区表");
            for row in partition_info {
                let partition_type: String = row.try_get("", "partitiontype")?;
                println!("  分区类型: {}", partition_type);
            }
        }
    }

    Ok(())
}
