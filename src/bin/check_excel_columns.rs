use calamine::{open_workbook, Reader, Xlsx};
use std::env;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        eprintln!("Usage: {} <excel_file>", args[0]);
        std::process::exit(1);
    }

    let file_path = &args[1];
    println!("Checking Excel file: {}", file_path);

    let mut workbook: Xlsx<_> = open_workbook(file_path)?;
    let sheet_names = workbook.sheet_names().to_owned();
    
    println!("Found {} sheets:", sheet_names.len());
    for (i, name) in sheet_names.iter().enumerate() {
        println!("  {}: {}", i + 1, name);
    }

    // 使用第一个工作表
    if let Some(sheet_name) = sheet_names.first() {
        println!("\nAnalyzing sheet: {}", sheet_name);
        
        if let Some(Ok(range)) = workbook.worksheet_range(sheet_name) {
            // 获取第一行（表头）
            if let Some(first_row) = range.rows().next() {
                println!("\nColumns found ({} total):", first_row.len());
                for (i, cell) in first_row.iter().enumerate() {
                    let value = cell.to_string();
                    if !value.trim().is_empty() {
                        println!("  {}: '{}'", i + 1, value);
                    }
                }
            } else {
                println!("No data found in the sheet");
            }
            
            println!("\nTotal rows: {}", range.height());
        } else {
            println!("Failed to read sheet: {}", sheet_name);
        }
    } else {
        println!("No sheets found in the workbook");
    }

    Ok(())
}
