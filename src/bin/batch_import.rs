use payermax::config::Config;
use payermax::service::BatchImportService;
use sea_orm::Database;
use std::env;
use std::path::Path;
use tracing::{error, info};
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 获取命令行参数
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        eprintln!(
            "Usage: {} <data_directory> [max_concurrent_files] [batch_size]",
            args[0]
        );
        eprintln!("Example: {} data 8 1000", args[0]);
        std::process::exit(1);
    }

    let data_dir = &args[1];
    let max_concurrent_files = args
        .get(2)
        .and_then(|s| s.parse::<usize>().ok())
        .unwrap_or(8);
    let batch_size = args
        .get(3)
        .and_then(|s| s.parse::<usize>().ok())
        .unwrap_or(1000);

    info!("Starting batch import:");
    info!("  Data directory: {}", data_dir);
    info!("  Max concurrent files: {}", max_concurrent_files);
    info!("  Batch size: {}", batch_size);

    // 检查数据目录是否存在
    let data_path = Path::new(data_dir);
    if !data_path.exists() {
        error!("Data directory '{}' does not exist", data_dir);
        std::process::exit(1);
    }

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env().unwrap_or_else(|e| {
        error!("Failed to load configuration: {:?}", e);
        std::process::exit(1);
    });

    // 连接数据库 - 配置连接池以支持高并发批量导入
    println!("Connecting to database: {}", config.database_url);

    let mut opt = sea_orm::ConnectOptions::new(&config.database_url);
    opt.max_connections(20) // 增加最大连接数，支持并发操作
        .min_connections(5) // 保持最小连接数
        .connect_timeout(std::time::Duration::from_secs(30)) // 连接超时30秒
        .acquire_timeout(std::time::Duration::from_secs(30)) // 获取连接超时30秒
        .idle_timeout(std::time::Duration::from_secs(300)) // 空闲连接5分钟超时
        .max_lifetime(std::time::Duration::from_secs(3600)) // 连接最大生命周期1小时
        .sqlx_logging(false); // 关闭sqlx详细日志，减少日志噪音

    let db = Database::connect(opt).await?;
    info!("Connected to database with connection pool (max: 20, min: 5)");

    // 创建批量导入服务
    let service = BatchImportService::new(db)
        .with_concurrency(max_concurrent_files)
        .with_batch_size(batch_size);

    // 执行批量导入
    match service.import_data_directory(data_path).await {
        Ok(progress) => {
            info!("Batch import completed successfully!");
            info!("  Total files: {}", progress.total_files);
            info!("  Processed files: {}", progress.processed_files);
            info!("  Total records: {}", progress.processed_records);

            if !progress.failed_files.is_empty() {
                error!("Failed files:");
                for failed_file in &progress.failed_files {
                    error!("  - {}", failed_file);
                }
            }
        }
        Err(e) => {
            error!("Batch import failed: {:?}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}
