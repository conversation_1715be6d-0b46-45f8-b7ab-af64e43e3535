use chrono::{NaiveDate, NaiveDateTime, Utc};
use entity::import_record::{ActiveModel as ImportRecordActiveModel, Entity as ImportRecord};
use entity::transactions::{Column as TransactionsColumn, Entity as Transactions};
use payermax::config::Config;
use payermax::service::file_parser::FileParser;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, Database, EntityTrait, PaginatorTrait,
    QueryFilter, QuerySelect, Set,
};
use std::collections::HashMap;
use std::path::Path;
use uuid::Uuid;

// 对比Excel文件和数据库记录的函数
async fn compare_excel_with_db(
    filename: &str,
    db_count: i64,
    db: &sea_orm::DatabaseConnection,
) -> Result<(), Box<dyn std::error::Error>> {
    let file_path = Path::new("data").join(filename);

    if !file_path.exists() {
        println!("⚠️  文件不存在: {}", file_path.display());
        return Ok(());
    }

    println!("\n🔍 详细对比文件: {}", filename);
    println!("📊 数据库记录数: {}", db_count);

    // 解析Excel文件
    match FileParser::parse_file(&file_path) {
        Ok(parse_result) => {
            let excel_rows = parse_result.rows.len();
            println!("📄 Excel数据行数: {}", excel_rows);
            println!("📋 Excel表头数: {}", parse_result.headers.len());

            // 显示实际的列名
            println!("\n📋 Excel文件的实际列名:");
            for (i, header) in parse_result.headers.iter().enumerate() {
                println!("  {}. {}", i + 1, header);
            }

            if excel_rows as i64 != db_count {
                let missing_count = excel_rows as i64 - db_count;
                println!(
                    "\n❌ 发现差异: Excel有{}行，数据库有{}行，差异{}行",
                    excel_rows, db_count, missing_count
                );

                // 分析失败原因
                analyze_import_failures(&parse_result, db, filename).await?;
            } else {
                println!("✅ 数据完整: Excel行数与数据库记录数一致");
            }
        }
        Err(e) => {
            println!("❌ 解析Excel文件失败: {}", e);
        }
    }

    Ok(())
}

// 分析导入失败的原因
async fn analyze_import_failures(
    parse_result: &payermax::service::file_parser::ParseResult,
    _db: &sea_orm::DatabaseConnection,
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔍 分析导入失败原因...");
    println!("模拟批量导入服务的转换过程...");

    let mut conversion_failures = HashMap::new();
    let mut successful_conversions = 0;
    let mut failed_conversions = 0;

    // 模拟批量导入服务的转换过程
    for (row_idx, row) in parse_result.rows.iter().enumerate() {
        // 尝试使用批量导入服务的转换逻辑
        match simulate_convert_to_transaction_record(filename, (row_idx + 1) as i32, &row.data) {
            Ok(_) => {
                successful_conversions += 1;
            }
            Err(e) => {
                failed_conversions += 1;
                conversion_failures.insert(row_idx + 2, e.to_string()); // +2 因为Excel从1开始且跳过表头
            }
        }
    }

    println!("\n📊 转换结果统计:");
    println!("  ✅ 成功转换: {} 行", successful_conversions);
    println!("  ❌ 转换失败: {} 行", failed_conversions);
    println!(
        "  📊 成功率: {:.2}%",
        (successful_conversions as f64 / parse_result.rows.len() as f64) * 100.0
    );

    if failed_conversions > 0 {
        // 统计失败原因
        let mut error_types = HashMap::new();
        for error_msg in conversion_failures.values() {
            *error_types.entry(error_msg.clone()).or_insert(0) += 1;
        }

        println!("\n🔸 失败原因统计:");
        for (error_type, count) in error_types.iter() {
            println!("  - {}: {} 行", error_type, count);
        }

        println!("\n🔸 转换失败详情 (显示前10行):");
        let mut count = 0;
        for (row_num, error) in conversion_failures.iter() {
            if count >= 10 {
                break;
            }
            println!("  Excel第{}行: {}", row_num, error);
            count += 1;
        }

        if conversion_failures.len() > 10 {
            println!("  ... 还有 {} 行转换失败", conversion_failures.len() - 10);
        }
    } else {
        println!("\n🤔 所有行都能成功转换，但数据库中缺少24,000条记录");
        println!("可能的原因:");
        println!("  1. 数据库插入过程中的错误（事务回滚）");
        println!("  2. 数据库约束冲突（虽然没有明显的唯一约束）");
        println!("  3. 分区表的限制或问题");
        println!("  4. 批量插入过程中的部分失败");

        // 建议进一步调查
        println!("\n💡 建议进一步调查:");
        println!("  1. 检查数据库日志中的错误信息");
        println!("  2. 重新导入该文件并观察详细日志");
        println!("  3. 检查是否有重复的 merchant_txn_id 或 trade_order_id");
        println!("  4. 验证分区表的完整性");

        // 检查数据库中的重复记录
        println!("\n🔍 检查可能的重复数据...");
        // 这里可以添加重复数据检查逻辑
    }

    Ok(())
}

// 模拟批量导入服务的转换逻辑
fn simulate_convert_to_transaction_record(
    _filename: &str,
    _row_number: i32,
    data: &HashMap<String, serde_json::Value>,
) -> Result<(), Box<dyn std::error::Error>> {
    // 辅助函数：从 Value 中提取字符串，支持多种可能的列名
    let get_string_flexible = |keys: &[&str]| -> Result<String, Box<dyn std::error::Error>> {
        for key in keys {
            if let Some(value) = data.get(*key) {
                if let Some(s) = value.as_str() {
                    return Ok(s.to_string());
                }
            }
        }
        Err(format!("Missing required field from: {:?}", keys).into())
    };

    // 辅助函数：从 Value 中提取 Decimal
    let get_decimal_flexible =
        |keys: &[&str]| -> Result<rust_decimal::Decimal, Box<dyn std::error::Error>> {
            let value_str = get_string_flexible(keys)?;

            // 清理数值字符串（移除逗号、货币符号等）
            let cleaned = value_str
                .replace(",", "")
                .replace("$", "")
                .replace("€", "")
                .replace("£", "")
                .replace("¥", "")
                .trim()
                .to_string();

            if cleaned.is_empty() {
                return Err(format!("Empty value for decimal field: {:?}", keys).into());
            }

            cleaned
                .parse::<rust_decimal::Decimal>()
                .map_err(|_| format!("Invalid decimal format for {:?}: {}", keys, value_str).into())
        };

    // 辅助函数：从 Value 中提取 DateTime
    let get_datetime_flexible =
        |keys: &[&str]| -> Result<chrono::DateTime<chrono::Utc>, Box<dyn std::error::Error>> {
            let value_str = get_string_flexible(keys)?;

            // 尝试多种日期格式
            let formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y/%m/%d %H:%M:%S",
                "%d/%m/%Y %H:%M:%S",
                "%m/%d/%Y %H:%M:%S",
                "%Y-%m-%d",
                "%Y/%m/%d",
                "%d/%m/%Y",
                "%m/%d/%Y",
            ];

            for format in &formats {
                if let Ok(dt) = chrono::NaiveDateTime::parse_from_str(&value_str, format) {
                    return Ok(dt.and_utc());
                }
                if let Ok(date) = chrono::NaiveDate::parse_from_str(&value_str, format) {
                    return Ok(date.and_hms_opt(0, 0, 0).unwrap().and_utc());
                }
            }

            Err(format!("Invalid datetime format for {:?}: {}", keys, value_str).into())
        };

    // 验证必需字段
    let _request_time = get_datetime_flexible(&["Request Time"])?;
    let _accounting_amount = get_decimal_flexible(&["Accounting Amount"])?;
    let _accounting_currency = get_string_flexible(&["Accounting Currency"])?;
    let _accounting_type = get_string_flexible(&["Accounting Type"])?;
    let _account_subject = get_string_flexible(&["Account Subject"])?;
    let _txn_create_time = get_datetime_flexible(&["Txn Create Time"])?;

    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 检查命令行参数
    let args: Vec<String> = std::env::args().collect();
    let specific_file = if args.len() > 1 {
        Some(args[1].clone())
    } else {
        None
    };

    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    // 如果指定了特定文件，只分析该文件
    if let Some(filename) = specific_file {
        println!("🎯 专门分析文件: {}", filename);

        // 查询该文件的记录数
        let db_count = Transactions::find()
            .filter(entity::transactions::Column::Filename.eq(&filename))
            .count(&db)
            .await?;

        if db_count == 0 {
            println!("❌ 数据库中没有找到文件: {}", filename);
            return Ok(());
        }

        // 详细对比该文件
        compare_excel_with_db(&filename, db_count as i64, &db).await?;
        return Ok(());
    }

    // 查询每个文件的记录数
    let results = Transactions::find()
        .select_only()
        .column_as(entity::transactions::Column::Filename, "filename")
        .column_as(entity::transactions::Column::Id.count(), "count")
        .group_by(entity::transactions::Column::Filename)
        .into_tuple::<(String, i64)>()
        .all(&db)
        .await?;

    println!("\n=== 导入结果统计与Excel对比 ===");
    let mut total_records = 0i64;
    let mut total_excel_rows = 0i64;
    let mut files_with_differences = 0;

    for (filename, count) in &results {
        println!("\n📁 文件: {} - 数据库记录数: {}", filename, count);
        total_records += count;

        // 对比Excel文件
        let file_path = Path::new("data").join(filename);
        if file_path.exists() {
            match FileParser::parse_file(&file_path) {
                Ok(parse_result) => {
                    let excel_rows = parse_result.rows.len() as i64;
                    total_excel_rows += excel_rows;

                    if excel_rows != *count {
                        let diff = excel_rows - count;
                        println!("  📄 Excel数据行数: {} (差异: {})", excel_rows, diff);
                        files_with_differences += 1;

                        if diff > 0 {
                            println!("  ⚠️  有{}行数据未成功导入", diff);
                        } else {
                            println!("  ⚠️  数据库记录比Excel多{}行", -diff);
                        }
                    } else {
                        println!("  ✅ Excel数据行数: {} (完全匹配)", excel_rows);
                    }
                }
                Err(e) => {
                    println!("  ❌ 无法解析Excel文件: {}", e);
                }
            }
        } else {
            println!("  ⚠️  Excel文件不存在: {}", file_path.display());
        }
    }

    println!("\n📊 总体统计:");
    println!("  数据库总记录数: {}", total_records);
    println!("  Excel总数据行数: {}", total_excel_rows);
    println!("  总文件数: {}", results.len());
    println!("  有差异的文件数: {}", files_with_differences);

    if total_excel_rows != total_records {
        let total_diff = total_excel_rows - total_records;
        println!("  📈 总体差异: {} 行", total_diff);
        if total_diff > 0 {
            println!("  ⚠️  总共有 {} 行数据未成功导入", total_diff);
        }
    } else {
        println!("  ✅ 所有文件数据完整匹配");
    }

    // 检查空值记录
    println!("\n=== 空值记录检查 ===");
    for (filename, _count) in &results {
        // 检查 Merchant Txn ID 为空的记录
        let empty_merchant_count = Transactions::find()
            .filter(entity::transactions::Column::Filename.eq(filename))
            .filter(entity::transactions::Column::MerchantTxnId.is_null())
            .count(&db)
            .await?;

        // 检查 Trade Order ID 为空的记录
        let empty_trade_count = Transactions::find()
            .filter(entity::transactions::Column::Filename.eq(filename))
            .filter(entity::transactions::Column::TradeOrderId.is_null())
            .count(&db)
            .await?;

        // 检查两个字段都为空的记录
        let both_empty_count = Transactions::find()
            .filter(entity::transactions::Column::Filename.eq(filename))
            .filter(entity::transactions::Column::MerchantTxnId.is_null())
            .filter(entity::transactions::Column::TradeOrderId.is_null())
            .count(&db)
            .await?;

        if empty_merchant_count > 0 || empty_trade_count > 0 {
            println!("文件: {}", filename);
            println!("  - Merchant Txn ID 为空: {} 条", empty_merchant_count);
            println!("  - Trade Order ID 为空: {} 条", empty_trade_count);
            println!("  - 两个字段都为空: {} 条", both_empty_count);
        }
    }

    // 查询分区信息
    let partition_query = r#"
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE tablename LIKE 'transactions_%' 
        ORDER BY tablename;
    "#;

    let partitions = db
        .query_all(sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            partition_query.to_string(),
        ))
        .await?;

    println!("\n=== 分区表信息 ===");
    for partition in partitions {
        let table_name: String = partition.try_get("", "tablename")?;
        println!("分区表: {}", table_name);
    }

    // 创建 import_record 记录
    println!("\n=== 创建 Import Record 记录 ===");
    let mut created_records = 0;
    let mut existing_records = 0;

    for (filename, count) in &results {
        // 检查是否已存在 import_record
        let existing = ImportRecord::find()
            .filter(entity::import_record::Column::FileName.eq(filename))
            .one(&db)
            .await?;

        if existing.is_some() {
            println!("Import record already exists for: {}", filename);
            existing_records += 1;
            continue;
        }

        // 获取文件信息
        let file_path = Path::new("data").join(filename);
        let file_size = if file_path.exists() {
            std::fs::metadata(&file_path)?.len() as i64
        } else {
            0 // 如果文件不存在，设为0
        };

        let file_extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown");

        // 创建 import_record
        let import_record = ImportRecordActiveModel {
            id: Set(Uuid::new_v4()),
            file_name: Set(filename.clone()),
            file_type: Set(file_extension.to_string()),
            file_size: Set(file_size),
            status: Set("completed".to_string()),
            total_rows: Set(Some(*count as i32)),
            processed_rows: Set(Some(*count as i32)),
            error_rows: Set(Some(0)),
            error_message: Set(None),
            created_at: Set(Utc::now().into()),
            updated_at: Set(Utc::now().into()),
        };

        match import_record.insert(&db).await {
            Ok(_) => {
                println!(
                    "✓ Created import record for: {} ({} records)",
                    filename, count
                );
                created_records += 1;
            }
            Err(e) => {
                println!("✗ Failed to create import record for {}: {}", filename, e);
            }
        }
    }

    println!("\n=== Import Record 创建结果 ===");
    println!("新创建记录: {}", created_records);
    println!("已存在记录: {}", existing_records);
    println!("总文件数: {}", results.len());

    if created_records > 0 {
        println!(
            "🎉 成功为 {} 个已导入文件创建了 import_record 记录！",
            created_records
        );
    }

    // 统计总的空值记录
    println!("\n=== 空值记录统计汇总 ===");
    let total_empty_merchant = Transactions::find()
        .filter(entity::transactions::Column::MerchantTxnId.is_null())
        .count(&db)
        .await?;

    let total_empty_trade = Transactions::find()
        .filter(entity::transactions::Column::TradeOrderId.is_null())
        .count(&db)
        .await?;

    let total_both_empty = Transactions::find()
        .filter(entity::transactions::Column::MerchantTxnId.is_null())
        .filter(entity::transactions::Column::TradeOrderId.is_null())
        .count(&db)
        .await?;

    println!(
        "全库 Merchant Txn ID 为空的记录: {} 条",
        total_empty_merchant
    );
    println!("全库 Trade Order ID 为空的记录: {} 条", total_empty_trade);
    println!("全库两个字段都为空的记录: {} 条", total_both_empty);
    println!("总记录数: {} 条", total_records);
    println!(
        "空值记录占比: {:.2}%",
        (total_empty_trade as f64 / total_records as f64) * 100.0
    );

    Ok(())
}
