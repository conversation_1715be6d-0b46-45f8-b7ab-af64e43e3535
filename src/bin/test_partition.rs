use entity::transactions::{Column as TransactionsColumn, Entity as Transactions};
use payermax::config::Config;
use sea_orm::{ColumnTrait, ConnectionTrait, Database, EntityTrait, PaginatorTrait, QueryFilter};
use std::time::Instant;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 加载配置
    dotenvy::dotenv().ok();
    let config = Config::from_env()?;

    // 连接数据库
    let db = Database::connect(&config.database_url).await?;
    println!("Connected to database");

    let filename = "SP20519158_bill_20240701_20250630_28.xlsx";
    println!("🎯 测试文件: {}", filename);

    // 生成分区表名
    let partition_name = sanitize_partition_name(filename);
    println!("🔍 预期分区名: transactions_{}", partition_name);

    // 检查分区是否存在
    let check_partition_sql = format!(
        "SELECT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'transactions_{}')",
        partition_name
    );

    println!("🔍 执行SQL: {}", check_partition_sql);

    let start = Instant::now();
    let partition_exists = db
        .query_one(sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            check_partition_sql,
        ))
        .await?;

    if let Some(row) = partition_exists {
        let exists: bool = row.try_get("", "exists")?;
        println!("🔍 分区表存在: {}", exists);

        if exists {
            // 使用分区表查询
            let count_sql = format!(
                "SELECT COUNT(*) as count FROM transactions_{} WHERE filename = '{}'",
                partition_name,
                filename.replace("'", "''")
            );

            println!("🔍 分区查询SQL: {}", count_sql);

            let result = db
                .query_one(sea_orm::Statement::from_string(
                    sea_orm::DatabaseBackend::Postgres,
                    count_sql,
                ))
                .await?;

            if let Some(row) = result {
                let count: i64 = row.try_get("", "count")?;
                let duration = start.elapsed();
                println!("✅ 分区查询结果: {} 条记录, 耗时: {:?}", count, duration);
            }
        } else {
            // 普通查询
            println!("🔄 使用普通查询");
            let count = Transactions::find()
                .filter(TransactionsColumn::Filename.eq(filename))
                .count(&db)
                .await?;
            let duration = start.elapsed();
            println!("✅ 普通查询结果: {} 条记录, 耗时: {:?}", count, duration);
        }
    }

    Ok(())
}

// 生成安全的分区名称
fn sanitize_partition_name(filename: &str) -> String {
    filename
        .to_lowercase() // 转换为小写
        .replace(".", "_")
        .replace("-", "_")
        .replace(" ", "_")
        .chars()
        .filter(|c| c.is_alphanumeric() || *c == '_')
        .collect()
}
