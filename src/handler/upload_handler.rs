use crate::service::ImportService;
use crate::Config;
use salvo::prelude::*;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use uuid::Uuid;

#[derive(Debug, Deserialize)]
pub struct UploadQuery {
    pub auto_import: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct UploadResponse {
    pub success: bool,
    pub message: String,
    pub file_id: Option<String>,
    pub import_id: Option<Uuid>,
}

#[handler]
pub async fn upload_file(req: &mut Request, res: &mut Response) -> Result<(), salvo::Error> {
    // For now, use default config - will be improved after entity generation
    let config = Config::from_env().unwrap();

    let query: UploadQuery = req.parse_queries().unwrap_or_default();

    // Handle multipart file upload
    let file = req.file("file").await;
    
    match file {
        Some(file) => {
            // Validate file type
            let file_name = file.name().unwrap_or("unknown");
            let file_path_buf = PathBuf::from(file_name);
            let extension = file_path_buf
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("");

            if !matches!(extension.to_lowercase().as_str(), "csv" | "xlsx" | "xls") {
                res.render(Json(UploadResponse {
                    success: false,
                    message: "Only CSV and Excel files are supported".to_string(),
                    file_id: None,
                    import_id: None,
                }));
                return Ok(());
            }

            // Generate unique file ID and save file
            let file_id = Uuid::new_v4().to_string();
            let file_path_buf2 = PathBuf::from(file_name);
            let file_extension = file_path_buf2
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("bin");
            let saved_filename = format!("{}.{}", file_id, file_extension);
            let file_path = PathBuf::from(&config.upload_dir).join(&saved_filename);

            // Ensure upload directory exists
            if let Some(parent) = file_path.parent() {
                tokio::fs::create_dir_all(parent).await.map_err(|e| {
                    salvo::Error::other(format!("Failed to create upload directory: {}", e))
                })?;
            }

            // Save the uploaded file
            match std::fs::copy(file.path(), &file_path) {
                Ok(_) => {
                    let mut response = UploadResponse {
                        success: true,
                        message: "File uploaded successfully".to_string(),
                        file_id: Some(file_id),
                        import_id: None,
                    };

                    // Auto-import will be implemented after entity generation
                    if query.auto_import.unwrap_or(false) {
                        response.message = "File uploaded. Auto-import will be available after entity generation".to_string();
                    }

                    res.render(Json(response));
                }
                Err(e) => {
                    res.render(Json(UploadResponse {
                        success: false,
                        message: format!("Failed to save file: {}", e),
                        file_id: None,
                        import_id: None,
                    }));
                }
            }
        }
        None => {
            res.render(Json(UploadResponse {
                success: false,
                message: "No file provided".to_string(),
                file_id: None,
                import_id: None,
            }));
        }
    }

    Ok(())
}

impl Default for UploadQuery {
    fn default() -> Self {
        Self { auto_import: None }
    }
}
