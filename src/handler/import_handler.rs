use crate::service::{ImportService, ImportStatus};
use crate::Config;
use salvo::prelude::*;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use uuid::Uuid;

#[derive(Debug, Deserialize)]
pub struct ImportRequest {
    pub file_id: String,
    pub file_name: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ImportResponse {
    pub success: bool,
    pub message: String,
    pub import_id: Option<Uuid>,
}

#[derive(Debug, Serialize)]
pub struct ImportStatusResponse {
    pub success: bool,
    pub data: Option<ImportStatus>,
    pub message: String,
}

#[handler]
pub async fn start_import(req: &mut Request, res: &mut Response) -> Result<(), salvo::Error> {
    // For now, use default config - will be improved after entity generation
    let config = Config::from_env().unwrap();

    let import_req: ImportRequest = req.parse_json().await.map_err(|e| {
        salvo::Error::other(format!("Invalid request body: {}", e))
    })?;

    // Construct file path
    let file_path = PathBuf::from(&config.upload_dir).join(&import_req.file_id);
    
    // Check if file exists
    if !file_path.exists() {
        res.render(Json(ImportResponse {
            success: false,
            message: "File not found".to_string(),
            import_id: None,
        }));
        return Ok(());
    }

    // Import functionality will be implemented after entity generation
    res.render(Json(ImportResponse {
        success: false,
        message: "Import functionality will be available after entity generation".to_string(),
        import_id: None,
    }));

    Ok(())
}

#[handler]
pub async fn get_import_status(req: &mut Request, res: &mut Response) -> Result<(), salvo::Error> {
    // Import status will be implemented after entity generation
    
    res.render(Json(ImportStatusResponse {
        success: false,
        data: None,
        message: "Import status will be available after entity generation".to_string(),
    }));

    Ok(())
}

#[handler]
pub async fn list_imports(_req: &mut Request, res: &mut Response) -> Result<(), salvo::Error> {
    // TODO: Implement list imports functionality
    // This would require additional database queries to list all import records
    
    res.render(Json(serde_json::json!({
        "success": true,
        "data": [],
        "message": "List imports not yet implemented"
    })));

    Ok(())
}
