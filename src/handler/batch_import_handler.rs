use crate::service::batch_import_service::{BatchImportService, ImportProgress};
use salvo::prelude::*;
use sea_orm::DatabaseConnection;
use serde::{Deserialize, Serialize};
use std::path::Path;
use tokio::fs;
use tracing::{error, info};

#[derive(Debug, Serialize)]
pub struct BatchImportResponse {
    pub success: bool,
    pub message: String,
    pub progress: Option<ImportProgress>,
}

#[derive(Debug, Deserialize)]
pub struct BatchImportRequest {
    pub data_directory: Option<String>,
    pub max_concurrent_files: Option<usize>,
    pub batch_size: Option<usize>,
}

#[derive(Debug, Serialize)]
pub struct BatchUploadResponse {
    pub success: bool,
    pub message: String,
    pub filename: String,
    pub import_progress: Option<ImportProgress>,
}

/// 批量导入 data 目录下的所有文件
#[handler]
pub async fn batch_import_data(
    req: &mut Request,
    res: &mut Response,
    depot: &mut Depot,
) -> Result<(), salvo::Error> {
    let db = match depot.get::<DatabaseConnection>("db") {
        Ok(db) => db.clone(),
        Err(_) => {
            let response = BatchImportResponse {
                success: false,
                message: "Database connection not found".to_string(),
                progress: None,
            };
            res.render(Json(response));
            return Ok(());
        }
    };

    let request: BatchImportRequest = req.parse_json().await.unwrap_or_default();

    let data_dir = request.data_directory.as_deref().unwrap_or("data");

    let data_path = Path::new(data_dir);
    if !data_path.exists() {
        let response = BatchImportResponse {
            success: false,
            message: format!("Data directory '{}' does not exist", data_dir),
            progress: None,
        };
        res.render(Json(response));
        return Ok(());
    }

    info!("Starting batch import from directory: {}", data_dir);

    let mut service = BatchImportService::new(db.clone());

    if let Some(max_concurrent) = request.max_concurrent_files {
        service = service.with_concurrency(max_concurrent);
    }

    if let Some(batch_size) = request.batch_size {
        service = service.with_batch_size(batch_size);
    }

    match service.import_data_directory(data_path).await {
        Ok(progress) => {
            let response = BatchImportResponse {
                success: true,
                message: format!(
                    "Batch import completed. Processed {}/{} files, {} records",
                    progress.processed_files, progress.total_files, progress.processed_records
                ),
                progress: Some(progress),
            };
            res.render(Json(response));
        }
        Err(e) => {
            error!("Batch import failed: {:?}", e);
            let response = BatchImportResponse {
                success: false,
                message: format!("Batch import failed: {}", e),
                progress: None,
            };
            res.render(Json(response));
        }
    }

    Ok(())
}

/// 上传文件并立即导入
#[handler]
pub async fn upload_and_import(
    req: &mut Request,
    res: &mut Response,
    depot: &mut Depot,
) -> Result<(), salvo::Error> {
    let db = match depot.get::<DatabaseConnection>("db") {
        Ok(db) => db.clone(),
        Err(_) => {
            let response = BatchUploadResponse {
                success: false,
                message: "Database connection not found".to_string(),
                filename: String::new(),
                import_progress: None,
            };
            res.render(Json(response));
            return Ok(());
        }
    };

    // 获取上传的文件
    let file = req.file("file").await;
    if file.is_none() {
        let response = BatchUploadResponse {
            success: false,
            message: "No file uploaded".to_string(),
            filename: String::new(),
            import_progress: None,
        };
        res.render(Json(response));
        return Ok(());
    }

    let file = file.unwrap();
    let filename = file.name().unwrap_or("unknown").to_string();

    // 验证文件类型
    if !is_valid_file_type(&filename) {
        let response = BatchUploadResponse {
            success: false,
            message: "Invalid file type. Only .xlsx, .xls, and .csv files are supported"
                .to_string(),
            filename,
            import_progress: None,
        };
        res.render(Json(response));
        return Ok(());
    }

    // 确保上传目录存在
    let upload_dir = Path::new("upload");
    if !upload_dir.exists() {
        if let Err(e) = fs::create_dir_all(upload_dir).await {
            error!("Failed to create upload directory: {:?}", e);
            let response = BatchUploadResponse {
                success: false,
                message: "Failed to create upload directory".to_string(),
                filename,
                import_progress: None,
            };
            res.render(Json(response));
            return Ok(());
        }
    }

    // 保存文件
    let file_path = upload_dir.join(&filename);
    match tokio::fs::copy(file.path(), &file_path).await {
        Ok(_) => {
            info!("File uploaded successfully: {:?}", file_path);
        }
        Err(e) => {
            error!("Failed to save uploaded file: {:?}", e);
            let response = BatchUploadResponse {
                success: false,
                message: "Failed to save uploaded file".to_string(),
                filename,
                import_progress: None,
            };
            res.render(Json(response));
            return Ok(());
        }
    }

    // 立即导入文件
    let service = BatchImportService::new(db.clone());
    match service.import_single_file(&file_path).await {
        Ok(progress) => {
            let response = BatchUploadResponse {
                success: true,
                message: format!(
                    "File uploaded and imported successfully. Processed {} records",
                    progress.processed_records
                ),
                filename,
                import_progress: Some(progress),
            };
            res.render(Json(response));
        }
        Err(e) => {
            error!("Failed to import uploaded file: {:?}", e);
            let response = BatchUploadResponse {
                success: false,
                message: format!("File uploaded but import failed: {}", e),
                filename,
                import_progress: None,
            };
            res.render(Json(response));
        }
    }

    Ok(())
}

/// 获取批量导入进度（如果需要异步处理）
#[handler]
pub async fn get_batch_import_status(
    _req: &mut Request,
    res: &mut Response,
    _depot: &mut Depot,
) -> Result<(), salvo::Error> {
    // 这里可以实现一个任务状态查询系统
    // 目前返回简单的状态信息
    let response = serde_json::json!({
        "message": "Batch import status endpoint - implement task tracking if needed"
    });
    res.render(Json(response));
    Ok(())
}

/// 验证文件类型
fn is_valid_file_type(filename: &str) -> bool {
    let valid_extensions = ["xlsx", "xls", "csv"];

    if let Some(extension) = Path::new(filename).extension() {
        if let Some(ext_str) = extension.to_str() {
            return valid_extensions.contains(&ext_str.to_lowercase().as_str());
        }
    }

    false
}

impl Default for BatchImportRequest {
    fn default() -> Self {
        Self {
            data_directory: None,
            max_concurrent_files: None,
            batch_size: None,
        }
    }
}
