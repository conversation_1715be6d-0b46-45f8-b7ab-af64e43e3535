# PayerMax 批量导入功能

本文档介绍如何使用 PayerMax 的高性能批量导入功能，支持分区表和多线程处理。

## 功能特性

✅ **分区表支持**：按文件名自动创建 PostgreSQL 分区，优化查询性能  
✅ **多线程处理**：支持 4-8 个线程并发处理文件，提升导入速度  
✅ **批量插入**：每批次 1000 条记录，减少数据库连接开销  
✅ **文件格式支持**：支持 .xlsx、.xls、.csv 格式  
✅ **错误处理**：完善的错误处理和进度跟踪  
✅ **Web API**：提供 RESTful API 接口  

## 数据库架构

### 分区表结构
```sql
-- 主分区表
CREATE TABLE transactions (
    id UUID NOT NULL,
    filename VARCHAR NOT NULL,
    row_number INTEGER NOT NULL,
    request_time TIMESTAMPTZ NOT NULL,
    accounting_amount DECIMAL(20,2) NOT NULL,
    balance DECIMAL(20,2) NOT NULL,
    -- ... 其他字段
    PRIMARY KEY (id, filename)
) PARTITION BY LIST (filename);

-- 自动创建的分区示例
CREATE TABLE transactions_sp20519158_bill_20220701_20230630_0_xlsx 
PARTITION OF transactions FOR VALUES IN ('SP20519158_bill_20220701_20230630_0.xlsx');
```

## 使用方法

### 1. 命令行批量导入

```bash
# 基本用法
cargo run --bin batch_import data

# 指定并发数和批次大小
cargo run --bin batch_import data 8 1000
#                              ^   ^ ^
#                              |   | 批次大小
#                              |   最大并发文件数
#                              数据目录
```

### 2. Web API 接口

启动服务器：
```bash
cargo run
```

#### 批量导入 data 目录
```bash
curl -X POST http://localhost:3000/api/v1/batch/import \
  -H "Content-Type: application/json" \
  -d '{
    "data_directory": "data",
    "max_concurrent_files": 8,
    "batch_size": 1000
  }'
```

#### 上传并导入单个文件
```bash
curl -X POST http://localhost:3000/api/v1/batch/upload \
  -F "file=@your_file.xlsx"
```

### 3. API 响应示例

```json
{
  "success": true,
  "message": "Batch import completed. Processed 10/10 files, 34000000 records",
  "progress": {
    "total_files": 10,
    "processed_files": 10,
    "total_records": 34000000,
    "processed_records": 34000000,
    "failed_files": [],
    "current_file": null
  }
}
```

## 性能优化

### 1. 分区裁剪
通过文件名查询时，PostgreSQL 会自动进行分区裁剪：
```sql
-- 只查询特定文件的数据，自动使用对应分区
SELECT * FROM transactions 
WHERE filename = 'SP20519158_bill_20220701_20230630_0.xlsx';
```

### 2. 并发配置
- **推荐并发数**：4-8 个线程
- **批次大小**：1000 条记录
- **适用场景**：3400万条数据的导入

### 3. 索引优化
```sql
-- 自动创建的索引
CREATE INDEX idx_transactions_filename ON transactions (filename);
CREATE INDEX idx_transactions_request_time ON transactions (request_time);
```

## 数据格式要求

### Excel/CSV 文件列映射
| 文件列名 | 数据库字段 | 类型 | 说明 |
|---------|-----------|------|------|
| Request Time | request_time | TIMESTAMPTZ | 请求时间 |
| Accounting Amount | accounting_amount | DECIMAL(20,2) | 记账金额 |
| Balance | balance | DECIMAL(20,2) | 余额 |
| Accounting Currency | accounting_currency | VARCHAR(3) | 记账币种 |
| Accounting Type | accounting_type | VARCHAR(32) | 记账类型 |
| Account Subject | account_subject | VARCHAR(64) | 科目 |
| Txn Create Time | txn_create_time | TIMESTAMPTZ | 交易创建时间 |
| Txn Complete Time | txn_complete_time | TIMESTAMPTZ | 交易完成时间 |
| Merchant Txn ID | merchant_txn_id | VARCHAR(64) | 商户交易ID |
| Trade Order ID | trade_order_id | VARCHAR(64) | 交易订单ID |
| Country | country | VARCHAR(2) | 国家代码 |
| Txn Amount | txn_amount | DECIMAL(20,2) | 交易金额 |
| Txn Currency | txn_currency | VARCHAR(3) | 交易币种 |
| Payee Txn Fee | payee_txn_fee | DECIMAL(20,2) | 收款方手续费 |
| Payee Txn Fee Currency | payee_txn_fee_currency | VARCHAR(3) | 收款方手续费币种 |
| Payer Txn Fee | payer_txn_fee | DECIMAL(20,2) | 付款方手续费 |
| Payer Txn Fee Currency | payer_txn_fee_currency | VARCHAR(3) | 付款方手续费币种 |
| Payee Tax | payee_tax | DECIMAL(20,2) | 收款方税费 |
| Payee Tax Currency | payee_tax_currency | VARCHAR(3) | 收款方税费币种 |
| Payer Tax | payer_tax | DECIMAL(20,2) | 付款方税费 |
| Payer Tax Currency | payer_tax_currency | VARCHAR(3) | 付款方税费币种 |
| Remark | remark | TEXT | 备注（可选） |

### 自动添加字段
- `filename`：文件名
- `row_number`：文件中的行号（除去表头）
- `id`：UUID 主键
- `created_at`：导入时间

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   检查 .env 文件中的 DATABASE_URL 配置
   ```

2. **文件格式不支持**
   ```
   确保文件是 .xlsx、.xls 或 .csv 格式
   ```

3. **分区创建失败**
   ```
   检查文件名是否包含特殊字符，系统会自动清理
   ```

4. **内存不足**
   ```
   减少 max_concurrent_files 参数
   减少 batch_size 参数
   ```

### 监控和日志

启用详细日志：
```bash
RUST_LOG=info cargo run --bin batch_import data
```

## 测试

运行测试脚本：
```bash
./test_batch_import.sh
```

检查导入结果：
```sql
-- 查看各文件导入的记录数
SELECT filename, COUNT(*) as record_count 
FROM transactions 
GROUP BY filename 
ORDER BY filename;

-- 查看分区信息
SELECT schemaname, tablename, partitionname 
FROM pg_partitions 
WHERE tablename = 'transactions';
```
