use calamine::{open_workbook, Reader, Xlsx};
use std::env;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let args: Vec<String> = env::args().collect();
    if args.len() < 2 {
        eprintln!("Usage: {} <excel_file>", args[0]);
        return Ok(());
    }

    let file_path = &args[1];
    println!("分析Excel文件: {}", file_path);

    let mut workbook: Xlsx<_> = open_workbook(file_path)?;
    
    // 获取第一个工作表
    let sheet_names = workbook.sheet_names().to_owned();
    if sheet_names.is_empty() {
        println!("Excel文件中没有工作表");
        return Ok(());
    }
    
    let sheet_name = &sheet_names[0];
    println!("工作表名称: {}", sheet_name);
    
    if let Some(Ok(range)) = workbook.worksheet_range(sheet_name) {
        let (height, width) = range.get_size();
        println!("Excel文件尺寸: {} 行 x {} 列", height, width);
        
        if height > 0 {
            let data_rows = height - 1; // 排除表头
            println!("数据行数（不含表头）: {}", data_rows);
            
            // 获取表头
            if width > 0 {
                println!("\n表头信息:");
                for col in 0..width {
                    if let Some(cell) = range.get_value((0, col)) {
                        println!("  列 {}: {}", col + 1, cell);
                    }
                }
            }
            
            // 检查空值情况
            let mut merchant_col = None;
            let mut trade_col = None;
            
            // 查找关键列
            for col in 0..width {
                if let Some(cell) = range.get_value((0, col)) {
                    let header = cell.to_string();
                    if header.contains("Merchant Txn ID") {
                        merchant_col = Some(col);
                    }
                    if header.contains("Trade Order ID") {
                        trade_col = Some(col);
                    }
                }
            }
            
            if let Some(col_idx) = merchant_col {
                let mut empty_count = 0;
                for row in 1..height {
                    if let Some(cell) = range.get_value((row, col_idx)) {
                        if cell.to_string().trim().is_empty() {
                            empty_count += 1;
                        }
                    } else {
                        empty_count += 1;
                    }
                }
                println!("\nMerchant Txn ID 为空的行数: {}", empty_count);
            }
            
            if let Some(col_idx) = trade_col {
                let mut empty_count = 0;
                for row in 1..height {
                    if let Some(cell) = range.get_value((row, col_idx)) {
                        if cell.to_string().trim().is_empty() {
                            empty_count += 1;
                        }
                    } else {
                        empty_count += 1;
                    }
                }
                println!("Trade Order ID 为空的行数: {}", empty_count);
            }
            
            // 显示前几行数据
            println!("\n前5行数据样本:");
            for row in 0..std::cmp::min(6, height) {
                print!("第{}行: ", row + 1);
                for col in 0..std::cmp::min(5, width) {
                    if let Some(cell) = range.get_value((row, col)) {
                        print!("{} | ", cell);
                    } else {
                        print!("空 | ");
                    }
                }
                println!();
            }
        }
    } else {
        println!("无法读取工作表: {}", sheet_name);
    }
    
    Ok(())
}
