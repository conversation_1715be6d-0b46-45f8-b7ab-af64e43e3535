# PostgreSQL 批量导入性能优化建议

## 当前问题分析

您遇到的 `Database(ConnectionAcquire(Timeout))` 错误主要是由于：

1. **连接池配置不当**：应用程序没有正确配置数据库连接池
2. **并发连接需求高**：4个并发文件处理 + 每个文件的多个数据库操作
3. **PostgreSQL连接数限制**：当前 `max_connections = 100`

## 已修复的应用程序问题

✅ **修改了 `src/bin/batch_import.rs`**：
- 配置了连接池：最大20个连接，最小5个连接
- 设置了合理的超时时间：连接超时30秒，获取连接超时30秒
- 配置了连接生命周期管理

## PostgreSQL 配置优化建议

### 1. 连接相关参数（需要重启PostgreSQL）

```sql
-- 在 postgresql.conf 中修改以下参数：

# 连接数配置
max_connections = 200                    # 从100增加到200
reserved_connections = 10                # 保留连接数

# 连接超时配置
authentication_timeout = 2min            # 认证超时时间
```

### 2. 内存相关参数（提高批量操作性能）

```sql
# 内存配置优化
shared_buffers = 512MB                   # 从256MB增加到512MB
work_mem = 16MB                          # 从4MB增加到16MB（批量操作需要更多内存）
maintenance_work_mem = 256MB             # 从64MB增加到256MB
effective_cache_size = 2GB               # 根据您的系统内存调整

# 批量写入优化
wal_buffers = 16MB                       # WAL缓冲区
checkpoint_completion_target = 0.9       # 检查点完成目标
```

### 3. 批量导入专用优化

```sql
# 临时关闭一些检查以提高导入速度（仅在批量导入时）
fsync = off                              # 关闭强制同步（仅导入时）
synchronous_commit = off                 # 关闭同步提交
full_page_writes = off                   # 关闭全页写入

# 注意：导入完成后需要恢复这些设置！
```

## 应用级别优化建议

### 1. 调整并发参数

```bash
# 减少并发文件数，增加批量大小
RUST_LOG=info cargo run --bin batch_import data 2 1000

# 或者保持当前参数，现在连接池已优化
RUST_LOG=info cargo run --bin batch_import data 4 500
```

### 2. 监控连接使用情况

```sql
-- 查看当前连接数
SELECT count(*) as active_connections FROM pg_stat_activity;

-- 查看连接详情
SELECT pid, usename, application_name, client_addr, state, query_start 
FROM pg_stat_activity 
WHERE state = 'active';
```

## 重启PostgreSQL服务

修改配置后需要重启PostgreSQL服务：

```bash
# Windows (如果使用Windows服务)
net stop postgresql-x64-16
net start postgresql-x64-16

# 或者使用pg_ctl
pg_ctl restart -D "C:\Program Files\PostgreSQL\16\data"
```

## 验证修复效果

1. **重启PostgreSQL**应用上述配置
2. **重新编译**应用程序：`cargo build --bin batch_import`
3. **测试批量导入**：`RUST_LOG=info cargo run --bin batch_import data 4 500`
4. **监控连接数**：观察是否还有连接超时错误

## 预期改善

- ✅ 消除 `ConnectionAcquire(Timeout)` 错误
- ✅ 提高批量导入性能
- ✅ 更好的并发处理能力
- ✅ 稳定的数据库连接管理

如果问题仍然存在，可以进一步：
1. 减少并发文件数到2个
2. 增加PostgreSQL的max_connections到300
3. 监控系统资源使用情况
